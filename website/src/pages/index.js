import Head from 'next/head'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { FaRocket, FaCoins, FaUsers, FaGift, FaTelegram, FaTwitter, FaDiscord, FaPlay, FaDownload, FaChartLine } from 'react-icons/fa'
import { useState, useEffect } from 'react'
import Navbar from '../components/Navbar'
import Footer from '../components/Footer'

export default function Home() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <>
      <Head>
        <title>Hehe Miner - The Ultimate Telegram Mining Game | Free Token Airdrop</title>
        <meta name="description" content="Join Hehe Miner, the most exciting Telegram mining game! Mine HEHE tokens every 4 hours, refer friends, and earn big rewards. Free airdrop coming Q1 2026!" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="keywords" content="telegram mining, crypto mining game, token airdrop, HEHE token, blockchain game, play to earn" />
        <link rel="icon" href="/favicon.ico" />
        <meta property="og:title" content="Hehe Miner - The Ultimate Telegram Mining Game" />
        <meta property="og:description" content="Mine HEHE tokens every 4 hours, refer friends, and earn big rewards. Free airdrop coming Q1 2026!" />
        <meta property="og:image" content="/images/hehe-miner-banner.jpg" />
        <meta property="og:url" content="https://hehe-miner.vercel.app" />
        <meta name="twitter:card" content="summary_large_image" />
      </Head>

      <Navbar />

      <main className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        {/* Hero Section */}
        <section className="relative overflow-hidden min-h-screen flex items-center">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/10 via-orange-500/10 to-red-500/10"></div>

          <div className="relative container mx-auto px-4 py-20">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Left Content */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="text-center lg:text-left"
              >
                <motion.h1
                  className="text-5xl md:text-7xl lg:text-8xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent mb-6"
                  animate={{ scale: [1, 1.02, 1] }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  HEHE MINER
                </motion.h1>

                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.8 }}
                  className="text-xl md:text-2xl text-white/90 mb-4"
                >
                  The Ultimate Telegram Mining Game
                </motion.p>

                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.8 }}
                  className="text-lg text-white/80 mb-8 max-w-2xl"
                >
                  Mine HEHE tokens every 4 hours, build your mining empire, refer friends, and prepare for the biggest airdrop in Telegram gaming history!
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8, duration: 0.5 }}
                  className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
                >
                  <a
                    href="https://t.me/HeheMinerBot"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    <FaPlay className="inline mr-2" />
                    Play Now on Telegram
                  </a>

                  <Link href="/airdrop" className="border-2 border-yellow-400/50 hover:border-yellow-400 text-yellow-400 hover:text-yellow-300 font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105">
                    <FaGift className="inline mr-2" />
                    Airdrop Info
                  </Link>
                </motion.div>

                {/* Stats */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.2, duration: 0.8 }}
                  className="grid grid-cols-3 gap-4 mt-12 text-center lg:text-left"
                >
                  <div>
                    <div className="text-2xl font-bold text-yellow-400">10K+</div>
                    <div className="text-sm text-white/60">Active Miners</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-yellow-400">1M+</div>
                    <div className="text-sm text-white/60">Tokens Mined</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-yellow-400">Q1 2026</div>
                    <div className="text-sm text-white/60">TGE Launch</div>
                  </div>
                </motion.div>
              </motion.div>

              {/* Right Content - App Preview */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="relative"
              >
                <div className="relative mx-auto w-80 h-96 bg-gradient-to-br from-gray-800 to-gray-900 rounded-3xl p-4 shadow-2xl">
                  <div className="w-full h-full bg-gradient-to-br from-purple-900 to-blue-900 rounded-2xl p-6 flex flex-col items-center justify-center text-center">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                      className="w-24 h-24 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mb-6"
                    >
                      <FaCoins className="text-3xl text-white" />
                    </motion.div>

                    <h3 className="text-xl font-bold text-white mb-2">Mining Active</h3>
                    <p className="text-white/80 text-sm mb-4">Next claim in 2h 34m</p>

                    <div className="bg-white/10 rounded-lg p-4 w-full">
                      <div className="text-2xl font-bold text-yellow-400">1,250 HEHE</div>
                      <div className="text-sm text-white/60">Ready to claim</div>
                    </div>
                  </div>
                </div>

                {/* Floating elements */}
                <motion.div
                  animate={{ y: [-10, 10, -10] }}
                  transition={{ duration: 3, repeat: Infinity }}
                  className="absolute -top-4 -right-4 w-16 h-16 bg-yellow-400/20 rounded-full flex items-center justify-center"
                >
                  <FaCoins className="text-yellow-400 text-xl" />
                </motion.div>

                <motion.div
                  animate={{ y: [10, -10, 10] }}
                  transition={{ duration: 4, repeat: Infinity }}
                  className="absolute -bottom-4 -left-4 w-12 h-12 bg-purple-400/20 rounded-full flex items-center justify-center"
                >
                  <FaRocket className="text-purple-400 text-lg" />
                </motion.div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-20 bg-black/20">
          <div className="container mx-auto px-4">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-center text-white mb-4"
            >
              Game Features
            </motion.h2>

            <motion.p
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-xl text-white/80 text-center mb-16 max-w-3xl mx-auto"
            >
              Experience the most engaging Telegram mining game with innovative features designed for maximum rewards
            </motion.p>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              {[
                {
                  icon: <FaRocket className="text-4xl text-yellow-400" />,
                  title: "4-Hour Mining Cycles",
                  description: "Perfect timing that fits your daily routine. Mine, claim, and repeat for maximum efficiency.",
                  highlight: "Easy & Convenient"
                },
                {
                  icon: <FaCoins className="text-4xl text-yellow-400" />,
                  title: "HEHE Token Rewards",
                  description: "Earn real HEHE tokens that will be distributed in our massive airdrop event in Q1 2026.",
                  highlight: "Real Value"
                },
                {
                  icon: <FaUsers className="text-4xl text-yellow-400" />,
                  title: "Referral System",
                  description: "Invite friends and earn 10% bonus from their mining rewards. Build your mining network!",
                  highlight: "Passive Income"
                },
                {
                  icon: <FaGift className="text-4xl text-yellow-400" />,
                  title: "Mining Upgrades",
                  description: "Increase your mining power with upgrades. Higher levels = more tokens per mining session.",
                  highlight: "Scale Your Earnings"
                },
                {
                  icon: <FaChartLine className="text-4xl text-yellow-400" />,
                  title: "Leaderboards",
                  description: "Compete with other miners globally. Top miners get exclusive rewards and recognition.",
                  highlight: "Competitive Fun"
                },
                {
                  icon: <FaTelegram className="text-4xl text-yellow-400" />,
                  title: "Telegram Native",
                  description: "Play directly in Telegram. No downloads, no installations. Just pure mining fun!",
                  highlight: "Instant Access"
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-xl p-6 text-center hover:from-white/20 hover:to-white/10 transition-all duration-300 border border-white/10"
                >
                  <div className="mb-4">{feature.icon}</div>
                  <div className="text-sm text-yellow-400 font-semibold mb-2">{feature.highlight}</div>
                  <h3 className="text-xl font-bold text-white mb-3">{feature.title}</h3>
                  <p className="text-white/80 leading-relaxed">{feature.description}</p>
                </motion.div>
              ))}
            </div>

            {/* How to Play Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-2xl p-8 text-center"
            >
              <h3 className="text-3xl font-bold text-white mb-6">How to Start Mining</h3>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="flex flex-col items-center">
                  <div className="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center text-black font-bold text-xl mb-4">1</div>
                  <h4 className="text-lg font-semibold text-white mb-2">Open Telegram</h4>
                  <p className="text-white/80">Click the "Play Now" button to launch Hehe Miner bot</p>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center text-black font-bold text-xl mb-4">2</div>
                  <h4 className="text-lg font-semibold text-white mb-2">Start Mining</h4>
                  <p className="text-white/80">Tap the mine button and start earning HEHE tokens</p>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center text-black font-bold text-xl mb-4">3</div>
                  <h4 className="text-lg font-semibold text-white mb-2">Claim & Repeat</h4>
                  <p className="text-white/80">Return every 4 hours to claim your rewards and mine again</p>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Roadmap Section */}
        <section className="py-20 bg-gradient-to-br from-purple-900/30 to-blue-900/30">
          <div className="container mx-auto px-4">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-center text-white mb-4"
            >
              Roadmap to Success
            </motion.h2>

            <motion.p
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-xl text-white/80 text-center mb-16 max-w-3xl mx-auto"
            >
              From Telegram launch to Token Generation Event - here's our journey to revolutionize mining games
            </motion.p>

            <div className="max-w-4xl mx-auto">
              {[
                {
                  quarter: "Q3 2025",
                  title: "Telegram Bot Launch",
                  status: "completed",
                  items: [
                    "Hehe Miner bot launched on Telegram",
                    "Basic mining mechanics implemented",
                    "Referral system activated",
                    "Community building started"
                  ]
                },
                {
                  quarter: "Q4 2025",
                  title: "Feature Expansion",
                  status: "in-progress",
                  items: [
                    "Mining power upgrades system",
                    "Leaderboards and competitions",
                    "Enhanced user interface",
                    "Partnership announcements"
                  ]
                },
                {
                  quarter: "Q1 2026",
                  title: "Token Generation Event (TGE)",
                  status: "upcoming",
                  items: [
                    "HEHE token launch on Polygon",
                    "Massive airdrop distribution",
                    "DEX listings and liquidity",
                    "Staking mechanisms activated"
                  ]
                },
                {
                  quarter: "Q2 2026",
                  title: "Ecosystem Growth",
                  status: "planned",
                  items: [
                    "Advanced gaming features",
                    "Cross-platform integration",
                    "NFT marketplace launch",
                    "DAO governance implementation"
                  ]
                }
              ].map((phase, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  className={`flex items-center mb-12 ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
                >
                  <div className={`flex-1 ${index % 2 === 0 ? 'pr-8' : 'pl-8'}`}>
                    <div className={`bg-gradient-to-br ${
                      phase.status === 'completed' ? 'from-green-500/20 to-green-600/20 border-green-400/30' :
                      phase.status === 'in-progress' ? 'from-yellow-500/20 to-orange-500/20 border-yellow-400/30' :
                      phase.status === 'upcoming' ? 'from-blue-500/20 to-purple-500/20 border-blue-400/30' :
                      'from-gray-500/20 to-gray-600/20 border-gray-400/30'
                    } backdrop-blur-sm rounded-xl p-6 border`}>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-2xl font-bold text-white">{phase.quarter}</h3>
                        <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                          phase.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                          phase.status === 'in-progress' ? 'bg-yellow-500/20 text-yellow-400' :
                          phase.status === 'upcoming' ? 'bg-blue-500/20 text-blue-400' :
                          'bg-gray-500/20 text-gray-400'
                        }`}>
                          {phase.status === 'completed' ? '✓ Completed' :
                           phase.status === 'in-progress' ? '⚡ In Progress' :
                           phase.status === 'upcoming' ? '🚀 Upcoming' :
                           '📋 Planned'}
                        </span>
                      </div>
                      <h4 className="text-xl font-semibold text-white mb-3">{phase.title}</h4>
                      <ul className="space-y-2">
                        {phase.items.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-center text-white/80">
                            <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3 flex-shrink-0"></div>
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div className="flex-shrink-0">
                    <div className={`w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold ${
                      phase.status === 'completed' ? 'bg-green-500 text-white' :
                      phase.status === 'in-progress' ? 'bg-yellow-500 text-black' :
                      phase.status === 'upcoming' ? 'bg-blue-500 text-white' :
                      'bg-gray-500 text-white'
                    }`}>
                      {index + 1}
                    </div>
                  </div>

                  <div className="flex-1"></div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-4 gap-8 text-center">
              {[
                { number: "10K+", label: "Active Miners", icon: <FaUsers className="text-3xl text-yellow-400 mb-2" /> },
                { number: "1M+", label: "Tokens Mined", icon: <FaCoins className="text-3xl text-yellow-400 mb-2" /> },
                { number: "70%", label: "Airdrop Allocation", icon: <FaGift className="text-3xl text-yellow-400 mb-2" /> },
                { number: "24/7", label: "Mining Available", icon: <FaRocket className="text-3xl text-yellow-400 mb-2" /> }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-xl p-8 hover:from-yellow-400/30 hover:to-orange-500/30 transition-all duration-300"
                >
                  <div className="flex justify-center">{stat.icon}</div>
                  <h3 className="text-4xl md:text-5xl font-bold text-yellow-400 mb-2">{stat.number}</h3>
                  <p className="text-white/80 text-lg">{stat.label}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-purple-600/20 to-blue-600/20">
          <div className="container mx-auto px-4 text-center">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-white mb-8"
            >
              Join the Mining Revolution
            </motion.h2>

            <motion.p
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-xl text-white/90 mb-8 max-w-3xl mx-auto"
            >
              Don't miss out on the biggest Telegram mining opportunity! Start mining HEHE tokens today and secure your spot in the Q1 2026 airdrop.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <motion.a
                href="https://t.me/HeheMinerBot"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.05 }}
                className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-black font-bold py-4 px-12 rounded-full text-xl transition-all duration-300 shadow-lg"
              >
                <FaTelegram className="inline mr-2" />
                Start Mining Now
              </motion.a>

              <Link href="/tokenomics" className="border-2 border-white/30 hover:border-white/60 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105">
                <FaChartLine className="inline mr-2" />
                View Tokenomics
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 1, duration: 0.6 }}
              className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto"
            >
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">70%</div>
                <div className="text-sm text-white/60">Community Airdrop</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">Q1 2026</div>
                <div className="text-sm text-white/60">Token Launch</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">Polygon</div>
                <div className="text-sm text-white/60">Blockchain</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">No Vesting</div>
                <div className="text-sm text-white/60">Immediate Access</div>
              </div>
            </motion.div>
          </div>
        </section>

        <Footer />
      </main>
    </>
  )
}
      </main>
    </>
  )
}

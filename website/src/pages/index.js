import Head from 'next/head'
import { motion } from 'framer-motion'
import { FaRocket, FaCoins, FaUsers, FaGift, FaTelegram, FaTwitter, FaDiscord } from 'react-icons/fa'
import { useState, useEffect } from 'react'

export default function Home() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <>
      <Head>
        <title><PERSON><PERSON>r - The Ultimate Telegram Mining Game</title>
        <meta name="description" content="Join Hehe Miner, the most exciting Telegram mining game! Mine tokens every 4 hours, refer friends, and earn big rewards. Free airdrop coming soon!" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        {/* Hero Section */}
        <section className="relative overflow-hidden">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative container mx-auto px-4 py-20">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              <motion.h1
                className="text-6xl md:text-8xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent mb-6"
                animate={{ scale: [1, 1.05, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                HEHE MINER
              </motion.h1>
              
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.8 }}
                className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto"
              >
                The Ultimate Telegram Mining Game - Mine Every 4 Hours, Earn Big Rewards!
              </motion.p>

              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.8, duration: 0.5 }}
                className="flex flex-col sm:flex-row gap-4 justify-center items-center"
              >
                <a
                  href="https://t.me/HeheMinerBot"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  <FaTelegram className="inline mr-2" />
                  Start Mining Now!
                </a>
                
                <a
                  href="#features"
                  className="border-2 border-white/30 hover:border-white/60 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105"
                >
                  Learn More
                </a>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-20 bg-black/20">
          <div className="container mx-auto px-4">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-center text-white mb-16"
            >
              Why Choose Hehe Miner?
            </motion.h2>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  icon: <FaRocket className="text-4xl text-yellow-400" />,
                  title: "Mine Every 4 Hours",
                  description: "Simple and rewarding mining system that fits your schedule"
                },
                {
                  icon: <FaCoins className="text-4xl text-yellow-400" />,
                  title: "Free Token Airdrop",
                  description: "Get free tokens just for participating in our mining game"
                },
                {
                  icon: <FaUsers className="text-4xl text-yellow-400" />,
                  title: "Refer & Earn",
                  description: "Invite friends and earn bonus rewards for every referral"
                },
                {
                  icon: <FaGift className="text-4xl text-yellow-400" />,
                  title: "Upgrade Power",
                  description: "Increase your mining power and earn even more rewards"
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center hover:bg-white/20 transition-all duration-300"
                >
                  <div className="mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-bold text-white mb-2">{feature.title}</h3>
                  <p className="text-white/80">{feature.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-3 gap-8 text-center">
              {[
                { number: "10K+", label: "Active Miners" },
                { number: "1M+", label: "Tokens Mined" },
                { number: "24/7", label: "Mining Available" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  className="bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-xl p-8"
                >
                  <h3 className="text-4xl md:text-5xl font-bold text-yellow-400 mb-2">{stat.number}</h3>
                  <p className="text-white/80 text-lg">{stat.label}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-purple-600/20 to-blue-600/20">
          <div className="container mx-auto px-4 text-center">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-white mb-8"
            >
              Ready to Start Mining?
            </motion.h2>
            
            <motion.p
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-xl text-white/90 mb-8 max-w-2xl mx-auto"
            >
              Join thousands of miners already earning rewards. Start your mining journey today!
            </motion.p>

            <motion.a
              href="https://t.me/HeheMinerBot"
              target="_blank"
              rel="noopener noreferrer"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              whileHover={{ scale: 1.05 }}
              className="inline-block bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-black font-bold py-4 px-12 rounded-full text-xl transition-all duration-300 shadow-lg"
            >
              <FaTelegram className="inline mr-2" />
              Launch Hehe Miner Bot
            </motion.a>
          </div>
        </section>

        {/* Footer */}
        <footer className="py-12 bg-black/40">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-4 md:mb-0">
                <h3 className="text-2xl font-bold text-yellow-400">Hehe Miner</h3>
                <p className="text-white/60">The Ultimate Telegram Mining Game</p>
              </div>
              
              <div className="flex space-x-6">
                <a href="https://t.me/HeheMinerBot" target="_blank" rel="noopener noreferrer" className="text-white/60 hover:text-white transition-colors">
                  <FaTelegram className="text-2xl" />
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <FaTwitter className="text-2xl" />
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <FaDiscord className="text-2xl" />
                </a>
              </div>
            </div>
            
            <div className="border-t border-white/20 mt-8 pt-8 text-center">
              <p className="text-white/60">&copy; 2024 Hehe Miner. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </main>
    </>
  )
}

import Head from 'next/head'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { FaRocket, FaCoins, FaUsers, FaGift, FaTelegram, FaChartLine, FaGamepad, FaCog, FaGlobe } from 'react-icons/fa'
import Navbar from '../components/Navbar'
import Footer from '../components/Footer'

export default function Roadmap() {
  const roadmapPhases = [
    {
      quarter: "Q3 2025",
      title: "Foundation & Launch",
      status: "completed",
      icon: <FaRocket className="text-3xl" />,
      description: "Establishing the core infrastructure and launching the Telegram bot",
      achievements: [
        "✅ Hehe Miner Telegram bot launched",
        "✅ Basic mining mechanics implemented", 
        "✅ User registration and authentication",
        "✅ Referral system activated",
        "✅ Community building initiated",
        "✅ Initial user base of 1K+ miners"
      ],
      metrics: {
        users: "1,000+",
        features: "Core Mining",
        platform: "Telegram"
      }
    },
    {
      quarter: "Q4 2025",
      title: "Feature Expansion",
      status: "in-progress",
      icon: <FaGamepad className="text-3xl" />,
      description: "Enhancing gameplay with advanced features and community engagement",
      achievements: [
        "🔄 Mining power upgrade system",
        "🔄 Leaderboards and competitions",
        "🔄 Enhanced user interface",
        "🔄 Partnership announcements",
        "🔄 Community events and rewards",
        "🔄 Advanced referral bonuses"
      ],
      metrics: {
        users: "10,000+",
        features: "Advanced Gaming",
        platform: "Telegram + Web"
      }
    },
    {
      quarter: "Q1 2026",
      title: "Token Generation Event",
      status: "upcoming",
      icon: <FaCoins className="text-3xl" />,
      description: "Official token launch and massive community airdrop distribution",
      achievements: [
        "🚀 HEHE token launch on Polygon",
        "🚀 700M token airdrop distribution",
        "🚀 DEX listings (Uniswap, SushiSwap)",
        "🚀 Staking mechanisms activated",
        "🚀 Liquidity pools established",
        "🚀 Smart contract audits completed"
      ],
      metrics: {
        users: "50,000+",
        features: "Token Economy",
        platform: "Multi-chain"
      }
    },
    {
      quarter: "Q2 2026",
      title: "Ecosystem Growth",
      status: "planned",
      icon: <FaGlobe className="text-3xl" />,
      description: "Expanding the ecosystem with advanced features and integrations",
      achievements: [
        "📋 CEX listings (major exchanges)",
        "📋 Advanced gaming features",
        "📋 NFT marketplace launch",
        "📋 DAO governance implementation",
        "📋 Cross-platform integration",
        "📋 Mobile app development"
      ],
      metrics: {
        users: "100,000+",
        features: "Full Ecosystem",
        platform: "Omni-channel"
      }
    },
    {
      quarter: "Q3 2026",
      title: "Innovation & Scale",
      status: "planned",
      icon: <FaCog className="text-3xl" />,
      description: "Pushing boundaries with cutting-edge features and global expansion",
      achievements: [
        "📋 AI-powered mining optimization",
        "📋 VR/AR gaming experiences",
        "📋 Global partnerships",
        "📋 Enterprise solutions",
        "📋 Educational initiatives",
        "📋 Sustainability programs"
      ],
      metrics: {
        users: "500,000+",
        features: "Next-Gen Tech",
        platform: "Metaverse Ready"
      }
    }
  ]

  const milestones = [
    {
      date: "September 2025",
      title: "Bot Launch",
      description: "Hehe Miner Telegram bot goes live",
      status: "completed"
    },
    {
      date: "December 2025", 
      title: "10K Users",
      description: "Reach 10,000 active miners milestone",
      status: "in-progress"
    },
    {
      date: "March 2026",
      title: "Token Launch",
      description: "HEHE token TGE and airdrop distribution",
      status: "upcoming"
    },
    {
      date: "June 2026",
      title: "100K Users",
      description: "Achieve 100,000 registered users",
      status: "planned"
    }
  ]

  return (
    <>
      <Head>
        <title>Hehe Miner Roadmap - From Q3 2025 Launch to Q2 2026 TGE | Development Timeline</title>
        <meta name="description" content="Follow Hehe Miner's journey from Telegram bot launch in Q3 2025 to Token Generation Event in Q1 2026. Detailed roadmap with milestones and features." />
        <meta name="keywords" content="hehe miner roadmap, telegram bot development, token launch timeline, Q1 2026 TGE, gaming roadmap" />
      </Head>

      <Navbar />

      <main className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 pt-16">
        {/* Hero Section */}
        <section className="py-20">
          <div className="container mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div
                animate={{ y: [-10, 10, -10] }}
                transition={{ duration: 3, repeat: Infinity }}
                className="inline-block text-6xl mb-6"
              >
                🗺️
              </motion.div>
              
              <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent mb-6">
                ROADMAP
              </h1>
              
              <p className="text-xl md:text-2xl text-white/90 mb-4 max-w-3xl mx-auto">
                Our Journey to Revolutionize Telegram Gaming
              </p>
              
              <p className="text-lg text-white/80 mb-8 max-w-2xl mx-auto">
                From humble beginnings in Q3 2025 to becoming the leading Telegram mining ecosystem by Q2 2026
              </p>

              <div className="grid md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-12">
                <div className="bg-gradient-to-br from-green-400/20 to-green-500/20 rounded-xl p-6">
                  <h3 className="text-2xl font-bold text-green-400 mb-2">Q3 2025</h3>
                  <p className="text-white/80">Bot Launch</p>
                </div>
                <div className="bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-xl p-6">
                  <h3 className="text-2xl font-bold text-yellow-400 mb-2">Q4 2025</h3>
                  <p className="text-white/80">Feature Expansion</p>
                </div>
                <div className="bg-gradient-to-br from-blue-400/20 to-purple-500/20 rounded-xl p-6">
                  <h3 className="text-2xl font-bold text-blue-400 mb-2">Q1 2026</h3>
                  <p className="text-white/80">Token Launch</p>
                </div>
                <div className="bg-gradient-to-br from-purple-400/20 to-pink-500/20 rounded-xl p-6">
                  <h3 className="text-2xl font-bold text-purple-400 mb-2">Q2 2026</h3>
                  <p className="text-white/80">Ecosystem Growth</p>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Detailed Roadmap */}
        <section className="py-20 bg-black/20">
          <div className="container mx-auto px-4">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-center text-white mb-16"
            >
              Development Timeline
            </motion.h2>

            <div className="space-y-12">
              {roadmapPhases.map((phase, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
                >
                  <div className={`flex-1 ${index % 2 === 0 ? 'pr-8' : 'pl-8'}`}>
                    <div className={`bg-gradient-to-br ${
                      phase.status === 'completed' ? 'from-green-500/20 to-green-600/20 border-green-400/30' :
                      phase.status === 'in-progress' ? 'from-yellow-500/20 to-orange-500/20 border-yellow-400/30' :
                      phase.status === 'upcoming' ? 'from-blue-500/20 to-purple-500/20 border-blue-400/30' :
                      'from-gray-500/20 to-gray-600/20 border-gray-400/30'
                    } backdrop-blur-sm rounded-xl p-8 border`}>
                      
                      <div className="flex items-center justify-between mb-6">
                        <div>
                          <h3 className="text-3xl font-bold text-white mb-2">{phase.quarter}</h3>
                          <h4 className="text-xl font-semibold text-yellow-400">{phase.title}</h4>
                        </div>
                        <span className={`px-4 py-2 rounded-full text-sm font-semibold ${
                          phase.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                          phase.status === 'in-progress' ? 'bg-yellow-500/20 text-yellow-400' :
                          phase.status === 'upcoming' ? 'bg-blue-500/20 text-blue-400' :
                          'bg-gray-500/20 text-gray-400'
                        }`}>
                          {phase.status === 'completed' ? '✅ Completed' :
                           phase.status === 'in-progress' ? '🔄 In Progress' :
                           phase.status === 'upcoming' ? '🚀 Upcoming' :
                           '📋 Planned'}
                        </span>
                      </div>

                      <p className="text-white/80 mb-6 text-lg">{phase.description}</p>

                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h5 className="text-white font-semibold mb-3">Key Achievements</h5>
                          <ul className="space-y-2">
                            {phase.achievements.map((achievement, achIndex) => (
                              <li key={achIndex} className="text-white/80 text-sm">
                                {achievement}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h5 className="text-white font-semibold mb-3">Metrics</h5>
                          <div className="space-y-3">
                            <div className="bg-white/5 rounded-lg p-3">
                              <div className="text-sm text-white/60">Target Users</div>
                              <div className="text-white font-semibold">{phase.metrics.users}</div>
                            </div>
                            <div className="bg-white/5 rounded-lg p-3">
                              <div className="text-sm text-white/60">Features</div>
                              <div className="text-white font-semibold">{phase.metrics.features}</div>
                            </div>
                            <div className="bg-white/5 rounded-lg p-3">
                              <div className="text-sm text-white/60">Platform</div>
                              <div className="text-white font-semibold">{phase.metrics.platform}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex-shrink-0">
                    <div className={`w-20 h-20 rounded-full flex items-center justify-center ${
                      phase.status === 'completed' ? 'bg-green-500 text-white' :
                      phase.status === 'in-progress' ? 'bg-yellow-500 text-black' :
                      phase.status === 'upcoming' ? 'bg-blue-500 text-white' :
                      'bg-gray-500 text-white'
                    }`}>
                      {phase.icon}
                    </div>
                  </div>
                  
                  <div className="flex-1"></div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Key Milestones */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-center text-white mb-16"
            >
              Key Milestones
            </motion.h2>

            <div className="max-w-4xl mx-auto">
              <div className="relative">
                {/* Timeline line */}
                <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-yellow-400 to-purple-500"></div>
                
                {milestones.map((milestone, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.2 }}
                    className={`flex items-center mb-12 ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
                  >
                    <div className={`flex-1 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8'}`}>
                      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                        <h3 className="text-xl font-bold text-white mb-2">{milestone.title}</h3>
                        <p className="text-white/80 mb-2">{milestone.description}</p>
                        <div className="text-sm text-yellow-400 font-semibold">{milestone.date}</div>
                      </div>
                    </div>
                    
                    <div className="flex-shrink-0">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center border-4 ${
                        milestone.status === 'completed' ? 'bg-green-500 border-green-400' :
                        milestone.status === 'in-progress' ? 'bg-yellow-500 border-yellow-400' :
                        milestone.status === 'upcoming' ? 'bg-blue-500 border-blue-400' :
                        'bg-gray-500 border-gray-400'
                      }`}>
                        {milestone.status === 'completed' ? '✓' :
                         milestone.status === 'in-progress' ? '⚡' :
                         milestone.status === 'upcoming' ? '🚀' : '📋'}
                      </div>
                    </div>
                    
                    <div className="flex-1"></div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-purple-600/20 to-blue-600/20">
          <div className="container mx-auto px-4 text-center">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-white mb-8"
            >
              Join Our Journey
            </motion.h2>
            
            <motion.p
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-xl text-white/90 mb-8 max-w-3xl mx-auto"
            >
              Be part of the revolution from day one. Start mining today and secure your place in the biggest Telegram gaming success story!
            </motion.p>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <motion.a
                href="https://t.me/HeheMinerBot"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.05 }}
                className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-black font-bold py-4 px-12 rounded-full text-xl transition-all duration-300 shadow-lg"
              >
                <FaTelegram className="inline mr-2" />
                Start Your Journey
              </motion.a>
              
              <Link href="/tokenomics" className="border-2 border-white/30 hover:border-white/60 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105">
                <FaChartLine className="inline mr-2" />
                View Tokenomics
              </Link>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </>
  )
}

import Head from 'next/head'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { FaGift, FaCoins, FaUsers, FaCalendarAlt, FaTelegram, FaRocket, FaChartPie, FaCheckCircle } from 'react-icons/fa'
import Navbar from '../components/Navbar'
import Footer from '../components/Footer'

export default function Airdrop() {
  const airdropPhases = [
    {
      phase: "Phase 1",
      title: "Early Miners",
      period: "Q3 2025 - Q4 2025",
      allocation: "30%",
      description: "Reward early adopters who started mining in the first months",
      requirements: ["Mine for at least 30 days", "Refer minimum 5 friends", "Maintain active mining"],
      status: "active"
    },
    {
      phase: "Phase 2", 
      title: "Community Builders",
      period: "Q4 2025 - Q1 2026",
      allocation: "25%",
      description: "Recognize community members who helped grow the ecosystem",
      requirements: ["Refer 20+ friends", "Active in community", "Complete special tasks"],
      status: "upcoming"
    },
    {
      phase: "Phase 3",
      title: "Final Distribution",
      period: "Q1 2026 TGE",
      allocation: "15%",
      description: "Final airdrop distribution at Token Generation Event",
      requirements: ["Hold mining rewards", "Complete KYC", "Claim during TGE window"],
      status: "planned"
    }
  ]

  const eligibilityRequirements = [
    {
      icon: <FaTelegram className="text-2xl text-blue-400" />,
      title: "Active Telegram User",
      description: "Must have a verified Telegram account and actively use the Hehe Miner bot"
    },
    {
      icon: <FaCoins className="text-2xl text-yellow-400" />,
      title: "Mining Activity",
      description: "Consistent mining activity with at least 100 total mining sessions"
    },
    {
      icon: <FaUsers className="text-2xl text-green-400" />,
      title: "Referral Participation",
      description: "Refer at least 3 friends to qualify for the basic airdrop allocation"
    },
    {
      icon: <FaCheckCircle className="text-2xl text-purple-400" />,
      title: "KYC Verification",
      description: "Complete identity verification process before TGE (details announced later)"
    }
  ]

  return (
    <>
      <Head>
        <title>HEHE Token Airdrop - Free Distribution Q1 2026 | Hehe Miner</title>
        <meta name="description" content="Join the biggest Telegram gaming airdrop! 70% of HEHE tokens distributed to community. No vesting, immediate access. Start mining now!" />
        <meta name="keywords" content="HEHE token airdrop, free crypto, telegram airdrop, mining rewards, Q1 2026 TGE" />
      </Head>

      <Navbar />

      <main className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 pt-16">
        {/* Hero Section */}
        <section className="py-20">
          <div className="container mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="inline-block text-6xl mb-6"
              >
                🎁
              </motion.div>
              
              <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent mb-6">
                HEHE AIRDROP
              </h1>
              
              <p className="text-xl md:text-2xl text-white/90 mb-4 max-w-3xl mx-auto">
                The Biggest Telegram Gaming Airdrop in History
              </p>
              
              <p className="text-lg text-white/80 mb-8 max-w-2xl mx-auto">
                70% of total HEHE token supply distributed to our amazing community. No vesting period, immediate access at TGE in Q1 2026.
              </p>

              <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12">
                <div className="bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-xl p-6">
                  <FaCoins className="text-4xl text-yellow-400 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-white mb-2">700M HEHE</h3>
                  <p className="text-white/80">Total Airdrop Allocation</p>
                </div>
                <div className="bg-gradient-to-br from-blue-400/20 to-purple-500/20 rounded-xl p-6">
                  <FaCalendarAlt className="text-4xl text-blue-400 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-white mb-2">Q1 2026</h3>
                  <p className="text-white/80">Token Generation Event</p>
                </div>
                <div className="bg-gradient-to-br from-green-400/20 to-blue-500/20 rounded-xl p-6">
                  <FaGift className="text-4xl text-green-400 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-white mb-2">No Vesting</h3>
                  <p className="text-white/80">Immediate Access</p>
                </div>
              </div>

              <motion.a
                href="https://t.me/HeheMinerBot"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.05 }}
                className="inline-block bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-black font-bold py-4 px-12 rounded-full text-xl transition-all duration-300 shadow-lg"
              >
                <FaTelegram className="inline mr-2" />
                Start Mining for Airdrop
              </motion.a>
            </motion.div>
          </div>
        </section>

        {/* Airdrop Phases */}
        <section className="py-20 bg-black/20">
          <div className="container mx-auto px-4">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-center text-white mb-16"
            >
              Airdrop Distribution Phases
            </motion.h2>

            <div className="grid lg:grid-cols-3 gap-8">
              {airdropPhases.map((phase, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  className={`bg-gradient-to-br ${
                    phase.status === 'active' ? 'from-green-500/20 to-green-600/20 border-green-400/30' :
                    phase.status === 'upcoming' ? 'from-yellow-500/20 to-orange-500/20 border-yellow-400/30' :
                    'from-blue-500/20 to-purple-500/20 border-blue-400/30'
                  } backdrop-blur-sm rounded-xl p-6 border relative overflow-hidden`}
                >
                  <div className="absolute top-4 right-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                      phase.status === 'active' ? 'bg-green-500/20 text-green-400' :
                      phase.status === 'upcoming' ? 'bg-yellow-500/20 text-yellow-400' :
                      'bg-blue-500/20 text-blue-400'
                    }`}>
                      {phase.status === 'active' ? '🟢 Active' :
                       phase.status === 'upcoming' ? '🟡 Upcoming' :
                       '🔵 Planned'}
                    </span>
                  </div>

                  <h3 className="text-2xl font-bold text-white mb-2">{phase.phase}</h3>
                  <h4 className="text-xl font-semibold text-yellow-400 mb-3">{phase.title}</h4>
                  <p className="text-white/80 mb-4">{phase.description}</p>
                  
                  <div className="mb-4">
                    <div className="text-sm text-white/60 mb-1">Period</div>
                    <div className="text-white font-semibold">{phase.period}</div>
                  </div>
                  
                  <div className="mb-6">
                    <div className="text-sm text-white/60 mb-1">Allocation</div>
                    <div className="text-2xl font-bold text-yellow-400">{phase.allocation}</div>
                  </div>

                  <div>
                    <div className="text-sm text-white/60 mb-2">Requirements</div>
                    <ul className="space-y-1">
                      {phase.requirements.map((req, reqIndex) => (
                        <li key={reqIndex} className="flex items-center text-white/80 text-sm">
                          <div className="w-2 h-2 bg-yellow-400 rounded-full mr-2 flex-shrink-0"></div>
                          {req}
                        </li>
                      ))}
                    </ul>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Eligibility Requirements */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-center text-white mb-16"
            >
              Eligibility Requirements
            </motion.h2>

            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {eligibilityRequirements.map((req, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm rounded-xl p-6 hover:bg-white/20 transition-all duration-300"
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">{req.icon}</div>
                    <div>
                      <h3 className="text-xl font-bold text-white mb-2">{req.title}</h3>
                      <p className="text-white/80">{req.description}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-purple-600/20 to-blue-600/20">
          <div className="container mx-auto px-4 text-center">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-white mb-8"
            >
              Don't Miss the Biggest Airdrop!
            </motion.h2>
            
            <motion.p
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-xl text-white/90 mb-8 max-w-3xl mx-auto"
            >
              Start mining today to secure your spot in the HEHE token airdrop. The earlier you start, the bigger your rewards!
            </motion.p>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <motion.a
                href="https://t.me/HeheMinerBot"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.05 }}
                className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-black font-bold py-4 px-12 rounded-full text-xl transition-all duration-300 shadow-lg"
              >
                <FaTelegram className="inline mr-2" />
                Start Mining Now
              </motion.a>
              
              <Link href="/tokenomics" className="border-2 border-white/30 hover:border-white/60 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105">
                <FaChartPie className="inline mr-2" />
                View Tokenomics
              </Link>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </>
  )
}

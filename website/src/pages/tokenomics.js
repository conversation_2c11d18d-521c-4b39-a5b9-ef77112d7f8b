import Head from 'next/head'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { FaCoins, FaUsers, FaHandshake, FaLock, FaChartPie, FaTelegram, FaRocket, FaGift } from 'react-icons/fa'
import Navbar from '../components/Navbar'
import Footer from '../components/Footer'

export default function Tokenomics() {
  const tokenDistribution = [
    {
      category: "Community Airdrop",
      percentage: 70,
      amount: "700,000,000",
      color: "from-yellow-400 to-orange-500",
      icon: <FaGift className="text-2xl" />,
      description: "Distributed to active miners and community members",
      vesting: "No vesting - immediate access at TGE"
    },
    {
      category: "Partners & Advisors",
      percentage: 10,
      amount: "100,000,000",
      color: "from-blue-400 to-purple-500",
      icon: <FaHandshake className="text-2xl" />,
      description: "Strategic partnerships and advisory support",
      vesting: "6 months cliff, 18 months linear vesting"
    },
    {
      category: "Team",
      percentage: 5,
      amount: "50,000,000",
      color: "from-green-400 to-blue-500",
      icon: <FaUsers className="text-2xl" />,
      description: "Core development team and contributors",
      vesting: "12 months cliff, 24 months linear vesting"
    },
    {
      category: "Liquidity & Treasury",
      percentage: 15,
      amount: "150,000,000",
      color: "from-purple-400 to-pink-500",
      icon: <FaLock className="text-2xl" />,
      description: "DEX liquidity, CEX listings, and treasury operations",
      vesting: "Strategic release based on milestones"
    }
  ]

  const tokenUtility = [
    {
      title: "Mining Rewards",
      description: "Earn HEHE tokens through regular mining activities in the Telegram bot",
      icon: <FaCoins className="text-3xl text-yellow-400" />
    },
    {
      title: "Staking Benefits",
      description: "Stake HEHE tokens to earn additional rewards and boost mining power",
      icon: <FaRocket className="text-3xl text-blue-400" />
    },
    {
      title: "Governance Rights",
      description: "Vote on important decisions affecting the Hehe Miner ecosystem",
      icon: <FaUsers className="text-3xl text-green-400" />
    },
    {
      title: "Premium Features",
      description: "Access exclusive game features and enhanced mining capabilities",
      icon: <FaGift className="text-3xl text-purple-400" />
    }
  ]

  const roadmapMilestones = [
    {
      phase: "Q1 2026",
      title: "Token Generation Event",
      items: ["HEHE token launch on Polygon", "Airdrop distribution", "DEX listings", "Staking launch"]
    },
    {
      phase: "Q2 2026", 
      title: "Ecosystem Expansion",
      items: ["CEX listings", "Advanced staking features", "DAO governance", "NFT integration"]
    },
    {
      phase: "Q3 2026",
      title: "Cross-Chain Integration",
      items: ["Multi-chain support", "Bridge development", "Enhanced utility", "Partnership expansion"]
    }
  ]

  return (
    <>
      <Head>
        <title>HEHE Token Tokenomics - Distribution & Utility | Hehe Miner</title>
        <meta name="description" content="Discover HEHE token distribution: 70% community airdrop, no vesting. Built on Polygon for low fees. Staking, governance, and premium features." />
        <meta name="keywords" content="HEHE tokenomics, token distribution, polygon blockchain, crypto staking, governance token" />
      </Head>

      <Navbar />

      <main className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 pt-16">
        {/* Hero Section */}
        <section className="py-20">
          <div className="container mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                className="inline-block text-6xl mb-6"
              >
                <FaChartPie className="text-yellow-400" />
              </motion.div>
              
              <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent mb-6">
                TOKENOMICS
              </h1>
              
              <p className="text-xl md:text-2xl text-white/90 mb-4 max-w-3xl mx-auto">
                Fair Distribution, Real Utility, Community First
              </p>
              
              <p className="text-lg text-white/80 mb-8 max-w-2xl mx-auto">
                HEHE token is designed with the community at its core. 70% goes directly to players with no vesting period.
              </p>

              <div className="grid md:grid-cols-4 gap-6 max-w-5xl mx-auto mb-12">
                <div className="bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-xl p-6">
                  <h3 className="text-2xl font-bold text-white mb-2">1B</h3>
                  <p className="text-white/80">Total Supply</p>
                </div>
                <div className="bg-gradient-to-br from-blue-400/20 to-purple-500/20 rounded-xl p-6">
                  <h3 className="text-2xl font-bold text-white mb-2">Polygon</h3>
                  <p className="text-white/80">Blockchain</p>
                </div>
                <div className="bg-gradient-to-br from-green-400/20 to-blue-500/20 rounded-xl p-6">
                  <h3 className="text-2xl font-bold text-white mb-2">70%</h3>
                  <p className="text-white/80">Community</p>
                </div>
                <div className="bg-gradient-to-br from-purple-400/20 to-pink-500/20 rounded-xl p-6">
                  <h3 className="text-2xl font-bold text-white mb-2">Q1 2026</h3>
                  <p className="text-white/80">TGE Launch</p>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Token Distribution */}
        <section className="py-20 bg-black/20">
          <div className="container mx-auto px-4">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-center text-white mb-16"
            >
              Token Distribution
            </motion.h2>

            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Pie Chart Representation */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8 }}
                className="relative"
              >
                <div className="w-80 h-80 mx-auto relative">
                  {/* Simplified pie chart using CSS */}
                  <div className="w-full h-full rounded-full relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500" style={{clipPath: 'polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 30%)'}}></div>
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500" style={{clipPath: 'polygon(50% 50%, 0% 30%, 0% 0%, 50% 0%)'}}></div>
                    <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-blue-500" style={{clipPath: 'polygon(50% 50%, 50% 0%, 60% 0%, 50% 50%)'}}></div>
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-500" style={{clipPath: 'polygon(50% 50%, 60% 0%, 100% 0%, 100% 20%)'}}></div>
                  </div>
                  
                  {/* Center circle */}
                  <div className="absolute inset-20 bg-gradient-to-br from-purple-900 to-blue-900 rounded-full flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-white">HEHE</div>
                      <div className="text-sm text-white/60">1B Total</div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Distribution Details */}
              <div className="space-y-6">
                {tokenDistribution.map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: 30 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className={`bg-gradient-to-r ${item.color} bg-opacity-20 backdrop-blur-sm rounded-xl p-6 border border-white/10`}
                  >
                    <div className="flex items-start space-x-4">
                      <div className={`bg-gradient-to-r ${item.color} p-3 rounded-lg text-white`}>
                        {item.icon}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-xl font-bold text-white">{item.category}</h3>
                          <span className="text-2xl font-bold text-white">{item.percentage}%</span>
                        </div>
                        <p className="text-white/80 mb-2">{item.description}</p>
                        <div className="text-sm text-white/60 mb-1">Amount: {item.amount} HEHE</div>
                        <div className="text-sm text-yellow-400 font-semibold">{item.vesting}</div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Token Utility */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-center text-white mb-16"
            >
              Token Utility
            </motion.h2>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {tokenUtility.map((utility, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center hover:bg-white/20 transition-all duration-300"
                >
                  <div className="mb-4">{utility.icon}</div>
                  <h3 className="text-xl font-bold text-white mb-3">{utility.title}</h3>
                  <p className="text-white/80">{utility.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Roadmap */}
        <section className="py-20 bg-black/20">
          <div className="container mx-auto px-4">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-center text-white mb-16"
            >
              Token Launch Roadmap
            </motion.h2>

            <div className="grid lg:grid-cols-3 gap-8">
              {roadmapMilestones.map((milestone, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10"
                >
                  <h3 className="text-2xl font-bold text-yellow-400 mb-2">{milestone.phase}</h3>
                  <h4 className="text-xl font-semibold text-white mb-4">{milestone.title}</h4>
                  <ul className="space-y-2">
                    {milestone.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-center text-white/80">
                        <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3 flex-shrink-0"></div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-purple-600/20 to-blue-600/20">
          <div className="container mx-auto px-4 text-center">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold text-white mb-8"
            >
              Be Part of the HEHE Economy
            </motion.h2>
            
            <motion.p
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-xl text-white/90 mb-8 max-w-3xl mx-auto"
            >
              Start mining today to earn your share of the 700M HEHE tokens allocated to the community. No vesting, immediate access at TGE!
            </motion.p>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <motion.a
                href="https://t.me/HeheMinerBot"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.05 }}
                className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-black font-bold py-4 px-12 rounded-full text-xl transition-all duration-300 shadow-lg"
              >
                <FaTelegram className="inline mr-2" />
                Start Mining HEHE
              </motion.a>
              
              <Link href="/airdrop" className="border-2 border-white/30 hover:border-white/60 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105">
                <FaGift className="inline mr-2" />
                Airdrop Details
              </Link>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </>
  )
}

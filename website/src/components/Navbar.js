import { useState } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { FaTelegram, FaBars, FaTimes, FaCoins, FaRocket, FaUsers, FaGift } from 'react-icons/fa'

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false)

  const navItems = [
    { name: 'Home', href: '/', icon: <FaRocket className="text-sm" /> },
    { name: 'Airdrop', href: '/airdrop', icon: <FaGift className="text-sm" /> },
    { name: 'Tokenomics', href: '/tokenomics', icon: <FaCoins className="text-sm" /> },
    { name: 'Roadmap', href: '/roadmap', icon: <FaUsers className="text-sm" /> },
  ]

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <motion.div
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.5 }}
              className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center"
            >
              <FaCoins className="text-white text-lg" />
            </motion.div>
            <span className="text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              Hehe Miner
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="flex items-center space-x-1 text-white/80 hover:text-white transition-colors duration-200 font-medium"
              >
                {item.icon}
                <span>{item.name}</span>
              </Link>
            ))}
          </div>

          {/* CTA Button */}
          <div className="hidden md:block">
            <a
              href="https://t.me/HeheMinerBot"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-bold py-2 px-6 rounded-full text-sm transition-all duration-300 transform hover:scale-105"
            >
              <FaTelegram className="inline mr-2" />
              Play Now
            </a>
          </div>

          {/* Mobile menu button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="md:hidden text-white/80 hover:text-white transition-colors"
          >
            {isOpen ? <FaTimes className="text-xl" /> : <FaBars className="text-xl" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        <motion.div
          initial={false}
          animate={{ height: isOpen ? 'auto' : 0, opacity: isOpen ? 1 : 0 }}
          transition={{ duration: 0.3 }}
          className="md:hidden overflow-hidden"
        >
          <div className="py-4 space-y-4">
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => setIsOpen(false)}
                className="flex items-center space-x-2 text-white/80 hover:text-white transition-colors duration-200 font-medium py-2"
              >
                {item.icon}
                <span>{item.name}</span>
              </Link>
            ))}
            <a
              href="https://t.me/HeheMinerBot"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold py-3 px-6 rounded-full text-sm transition-all duration-300 mt-4"
            >
              <FaTelegram className="mr-2" />
              Play Now
            </a>
          </div>
        </motion.div>
      </div>
    </nav>
  )
}

import Link from 'next/link'
import { motion } from 'framer-motion'
import { FaTelegram, FaTwitter, FaDiscord, FaGithub, FaCoins, FaRocket, FaUsers, FaGift, FaChartLine } from 'react-icons/fa'

export default function Footer() {
  const quickLinks = [
    { name: 'Home', href: '/' },
    { name: 'Airdrop', href: '/airdrop' },
    { name: 'Tokenomics', href: '/tokenomics' },
    { name: 'Roadmap', href: '/roadmap' },
  ]

  const gameFeatures = [
    { name: 'Mining Guide', href: '/guide' },
    { name: 'Leaderboard', href: '/leaderboard' },
    { name: 'Referrals', href: '/referrals' },
    { name: 'Upgrades', href: '/upgrades' },
  ]

  const socialLinks = [
    { name: 'Telegram', href: 'https://t.me/HeheMinerBot', icon: <FaTelegram /> },
    { name: 'Twitter', href: '#', icon: <FaTwitter /> },
    { name: 'Discord', href: '#', icon: <FaDiscord /> },
    { name: 'GitH<PERSON>', href: '#', icon: <FaGithub /> },
  ]

  return (
    <footer className="bg-black/40 border-t border-white/10">
      <div className="container mx-auto px-4 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="md:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
                className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center"
              >
                <FaCoins className="text-white text-lg" />
              </motion.div>
              <span className="text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                Hehe Miner
              </span>
            </div>
            <p className="text-white/60 mb-4 leading-relaxed">
              The ultimate Telegram mining game. Mine HEHE tokens every 4 hours and prepare for the biggest airdrop in gaming history.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-white/60 hover:text-yellow-400 transition-colors duration-200 text-xl"
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <FaRocket className="mr-2 text-yellow-400" />
              Quick Links
            </h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-white/60 hover:text-white transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Game Features */}
          <div>
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <FaUsers className="mr-2 text-yellow-400" />
              Game Features
            </h3>
            <ul className="space-y-2">
              {gameFeatures.map((feature) => (
                <li key={feature.name}>
                  <Link
                    href={feature.href}
                    className="text-white/60 hover:text-white transition-colors duration-200"
                  >
                    {feature.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Token Info */}
          <div>
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <FaGift className="mr-2 text-yellow-400" />
              Token Info
            </h3>
            <div className="space-y-3">
              <div className="bg-white/5 rounded-lg p-3">
                <div className="text-sm text-white/60">Total Supply</div>
                <div className="text-white font-semibold">1,000,000,000 HEHE</div>
              </div>
              <div className="bg-white/5 rounded-lg p-3">
                <div className="text-sm text-white/60">Airdrop Allocation</div>
                <div className="text-white font-semibold">70% (No Vesting)</div>
              </div>
              <div className="bg-white/5 rounded-lg p-3">
                <div className="text-sm text-white/60">TGE Date</div>
                <div className="text-white font-semibold">Q1 2026</div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-white/10 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-white/60 text-sm mb-4 md:mb-0">
              &copy; 2025 Hehe Miner. All rights reserved. Built on Polygon blockchain.
            </div>
            
            <div className="flex items-center space-x-6 text-sm">
              <Link href="/privacy" className="text-white/60 hover:text-white transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-white/60 hover:text-white transition-colors">
                Terms of Service
              </Link>
              <a
                href="https://t.me/HeheMinerBot"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black font-bold py-2 px-4 rounded-full text-xs transition-all duration-300 transform hover:scale-105"
              >
                <FaTelegram className="inline mr-1" />
                Start Mining
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

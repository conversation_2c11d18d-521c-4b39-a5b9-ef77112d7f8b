const fs = require('fs');
const path = require('path');

// Try to use <PERSON> for conversion
let sharp;
try {
    sharp = require('sharp');
    console.log('✅ Sharp library found - will convert SVG to PNG');
} catch (e) {
    console.log('❌ Sharp not available, will provide alternative instructions');
}

async function convertSvgToPng() {
    const banners = [
        {
            svg: 'hehe-airdrop-banner.svg',
            png: 'hehe-airdrop-banner.png',
            jpg: 'hehe-airdrop-banner.jpg',
            name: 'Square Banner (1080x1080)'
        },
        {
            svg: 'hehe-airdrop-banner-horizontal.svg',
            png: 'hehe-airdrop-banner-horizontal.png',
            jpg: 'hehe-airdrop-banner-horizontal.jpg',
            name: 'Horizontal Banner (1200x630)'
        }
    ];
    
    try {
        if (sharp) {
            console.log('🎨 Converting banners to PNG and JPEG...\n');

            for (const banner of banners) {
                const svgPath = path.join(__dirname, 'public', banner.svg);
                const pngPath = path.join(__dirname, 'public', banner.png);
                const jpegPath = path.join(__dirname, 'public', banner.jpg);

                console.log(`📋 Processing: ${banner.name}`);

                // Read SVG file
                const svgBuffer = fs.readFileSync(svgPath);

                // Convert to PNG (high quality)
                await sharp(svgBuffer)
                    .png({ quality: 100, compressionLevel: 0 })
                    .toFile(pngPath);

                console.log(`✅ PNG created: ${banner.png}`);

                // Convert to JPEG (for smaller file size)
                await sharp(svgBuffer)
                    .jpeg({ quality: 95, progressive: true })
                    .toFile(jpegPath);

                console.log(`✅ JPEG created: ${banner.jpg}`);

                // Get file sizes
                const pngStats = fs.statSync(pngPath);
                const jpegStats = fs.statSync(jpegPath);

                console.log(`📊 PNG Size: ${(pngStats.size / 1024 / 1024).toFixed(2)} MB`);
                console.log(`📊 JPEG Size: ${(jpegStats.size / 1024 / 1024).toFixed(2)} MB\n`);
            }
            
        } else {
            console.log('\n🔧 Manual Conversion Instructions:');
            console.log('Since Sharp is not available, you can convert manually:');
            console.log('1. Open the SVG file in a browser');
            console.log('2. Take a screenshot or use browser dev tools');
            console.log('3. Or use online converters like:');
            console.log('   - https://convertio.co/svg-png/');
            console.log('   - https://cloudconvert.com/svg-to-png');
            console.log('   - https://www.freeconvert.com/svg-to-png');
        }
        
            console.log('🎯 Banner Specifications:');
            console.log('• Square Banner: 1080x1080 pixels (Instagram/Social Media)');
            console.log('• Horizontal Banner: 1200x630 pixels (Facebook/Twitter/LinkedIn)');
            console.log('• Format: PNG (high quality) or JPEG (smaller file)');
            console.log('• Purpose: HEHE Token airdrop advertisement');
            console.log('• Features: Professional branding, airdrop messaging, call-to-action');

            console.log('\n📱 Usage Recommendations:');
            console.log('• Instagram Posts: Use square PNG version');
            console.log('• Facebook/Twitter: Use horizontal JPEG for faster loading');
            console.log('• Telegram Channels: Use JPEG (better compression)');
            console.log('• Website Headers: Use horizontal PNG for best quality');
            console.log('• LinkedIn Posts: Use horizontal version');
        
    } catch (error) {
        console.error('❌ Error during conversion:', error.message);
        
        console.log('\n🔧 Alternative Solutions:');
        console.log('1. Install Sharp: npm install sharp');
        console.log('2. Use online SVG to PNG converters');
        console.log('3. Open SVG in browser and screenshot');
        console.log('4. Use design tools like Figma, Canva, or Photoshop');
    }
}

// Run the conversion
convertSvgToPng();

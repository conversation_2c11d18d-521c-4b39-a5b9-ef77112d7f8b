<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>he Miner Promotional Banner</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@400;700;900&display=swap');
        
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            font-family: 'Roboto', sans-serif;
        }
        
        .banner {
            width: 1200px;
            height: 630px;
            position: relative;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
            margin: 0 auto;
        }
        
        /* Animated background particles */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        
        .particle {
            position: absolute;
            background: #FFD700;
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .particle:nth-child(1) { width: 8px; height: 8px; left: 10%; top: 20%; animation-delay: 0s; }
        .particle:nth-child(2) { width: 12px; height: 12px; left: 20%; top: 80%; animation-delay: 1s; }
        .particle:nth-child(3) { width: 6px; height: 6px; left: 80%; top: 30%; animation-delay: 2s; }
        .particle:nth-child(4) { width: 10px; height: 10px; left: 90%; top: 70%; animation-delay: 3s; }
        .particle:nth-child(5) { width: 14px; height: 14px; left: 60%; top: 10%; animation-delay: 4s; }
        .particle:nth-child(6) { width: 8px; height: 8px; left: 30%; top: 60%; animation-delay: 5s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }
        
        /* Main content container */
        .content {
            position: relative;
            z-index: 10;
            height: 100%;
            display: flex;
            align-items: center;
            padding: 0 60px;
        }
        
        /* Left side - Logo and character */
        .left-side {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        /* Miner character */
        .miner-character {
            width: 200px;
            height: 200px;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 
                0 0 40px rgba(255, 215, 0, 0.6),
                inset 0 0 20px rgba(255, 255, 255, 0.2);
            animation: pulse 3s ease-in-out infinite;
            margin-bottom: 20px;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); box-shadow: 0 0 40px rgba(255, 215, 0, 0.6); }
            50% { transform: scale(1.05); box-shadow: 0 0 60px rgba(255, 215, 0, 0.8); }
        }
        
        .helmet {
            position: absolute;
            top: 30px;
            width: 100px;
            height: 60px;
            background: linear-gradient(135deg, #4A5568 0%, #2D3748 100%);
            border-radius: 50px 50px 20px 20px;
            border: 3px solid #1A202C;
        }
        
        .helmet-light {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #FFFF99 0%, #FFD700 70%, #FFA500 100%);
            border-radius: 50%;
            box-shadow: 0 0 20px #FFD700;
            animation: blink 2s ease-in-out infinite;
        }
        
        @keyframes blink {
            0%, 90%, 100% { opacity: 1; }
            95% { opacity: 0.3; }
        }
        
        .face {
            width: 80px;
            height: 80px;
            background: #FFD700;
            border-radius: 50%;
            position: relative;
            top: 20px;
            border: 3px solid #FFA500;
        }
        
        .eyes {
            position: absolute;
            top: 25px;
        }
        
        .eye {
            width: 8px;
            height: 12px;
            background: #2D3748;
            border-radius: 50%;
            position: absolute;
        }
        
        .eye.left { left: 20px; }
        .eye.right { right: 20px; }
        
        .smile {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 20px;
            border: 4px solid #2D3748;
            border-top: none;
            border-radius: 0 0 40px 40px;
        }
        
        /* Right side - Text content */
        .right-side {
            flex: 1.5;
            padding-left: 40px;
        }
        
        .main-title {
            font-family: 'Orbitron', monospace;
            font-size: 72px;
            font-weight: 900;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
            margin: 0;
            line-height: 1;
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5)); }
            to { filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8)); }
        }
        
        .subtitle {
            font-family: 'Orbitron', monospace;
            font-size: 36px;
            font-weight: 700;
            color: #00FF88;
            margin: 10px 0 30px 0;
            text-shadow: 0 0 15px rgba(0, 255, 136, 0.5);
        }
        
        .features {
            list-style: none;
            padding: 0;
            margin: 0 0 30px 0;
        }
        
        .feature {
            font-size: 24px;
            font-weight: 700;
            color: #FFFFFF;
            margin: 15px 0;
            display: flex;
            align-items: center;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .feature::before {
            content: "⚡";
            font-size: 28px;
            margin-right: 15px;
            color: #FFD700;
            animation: sparkle 1.5s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }
        
        .cta-button {
            background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 50%, #FF8E8E 100%);
            color: white;
            font-family: 'Orbitron', monospace;
            font-size: 28px;
            font-weight: 900;
            padding: 20px 40px;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            text-transform: uppercase;
            letter-spacing: 2px;
            box-shadow: 
                0 10px 30px rgba(255, 68, 68, 0.4),
                inset 0 2px 10px rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            animation: buttonPulse 3s ease-in-out infinite;
            margin-top: 20px;
        }
        
        @keyframes buttonPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .bot-link {
            font-size: 18px;
            color: #00FF88;
            margin-top: 15px;
            font-weight: 700;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
        }
        
        /* Decorative elements */
        .coin {
            position: absolute;
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            border-radius: 50%;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
            animation: coinSpin 4s linear infinite;
        }
        
        @keyframes coinSpin {
            0% { transform: rotateY(0deg); }
            100% { transform: rotateY(360deg); }
        }
        
        .coin1 { top: 50px; right: 100px; animation-delay: 0s; }
        .coin2 { bottom: 80px; left: 50px; animation-delay: 1s; }
        .coin3 { top: 150px; left: 200px; animation-delay: 2s; }
        
        /* Sparkle effects */
        .sparkle {
            position: absolute;
            color: #FFD700;
            font-size: 20px;
            animation: twinkle 2s ease-in-out infinite;
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1); }
        }
        
        .sparkle1 { top: 100px; right: 200px; animation-delay: 0s; }
        .sparkle2 { bottom: 200px; left: 100px; animation-delay: 0.5s; }
        .sparkle3 { top: 300px; right: 50px; animation-delay: 1s; }
        .sparkle4 { bottom: 100px; right: 300px; animation-delay: 1.5s; }
    </style>
</head>
<body>
    <div class="banner" id="banner">
        <!-- Animated background -->
        <div class="particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>

        <!-- Main content -->
        <div class="content">
            <!-- Left side - Character -->
            <div class="left-side">
                <div class="miner-character">
                    <div class="helmet">
                        <div class="helmet-light"></div>
                    </div>
                    <div class="face">
                        <div class="eyes">
                            <div class="eye left"></div>
                            <div class="eye right"></div>
                        </div>
                        <div class="smile"></div>
                    </div>
                </div>
            </div>

            <!-- Right side - Text -->
            <div class="right-side">
                <h1 class="main-title">HEHE</h1>
                <h2 class="subtitle">MINER</h2>

                <ul class="features">
                    <li class="feature">Mine Every 4 Hours</li>
                    <li class="feature">Free Token Airdrop</li>
                    <li class="feature">Refer Friends & Earn</li>
                    <li class="feature">Upgrade Mining Power</li>
                </ul>

                <button class="cta-button">Start Mining Now!</button>
                <div class="bot-link">t.me/HeheMinerBot</div>
            </div>
        </div>

        <!-- Decorative elements -->
        <div class="coin coin1"></div>
        <div class="coin coin2"></div>
        <div class="coin coin3"></div>

        <div class="sparkle sparkle1">✨</div>
        <div class="sparkle sparkle2">⭐</div>
        <div class="sparkle sparkle3">💎</div>
        <div class="sparkle sparkle4">✨</div>
    </div>
</body>
</html>

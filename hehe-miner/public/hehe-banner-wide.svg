<svg width="1200" height="300" viewBox="0 0 1200 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="wideBgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0F1419;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#1A202C;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2D3748;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#1A202C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0F1419;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="wideLogoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="wideHelmetGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="wideTextGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
    <filter id="wideGlow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter id="wideShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="6" stdDeviation="4" flood-color="#000000" flood-opacity="0.4"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="300" fill="url(#wideBgGradient)" rx="20"/>
  
  <!-- Background pattern -->
  <g opacity="0.1">
    <pattern id="hexPattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <polygon points="20,5 35,15 35,25 20,35 5,25 5,15" fill="none" stroke="#FFD700" stroke-width="1"/>
    </pattern>
    <rect width="1200" height="300" fill="url(#hexPattern)"/>
  </g>
  
  <!-- Left decorative elements -->
  <g transform="translate(80, 80)" opacity="0.4">
    <circle cx="0" cy="0" r="8" fill="#FFD700"/>
    <circle cx="30" cy="20" r="6" fill="#FFA500"/>
    <circle cx="60" cy="10" r="4" fill="#FF8C00"/>
  </g>
  
  <!-- Right decorative elements -->
  <g transform="translate(1040, 80)" opacity="0.4">
    <circle cx="0" cy="0" r="8" fill="#FFD700"/>
    <circle cx="30" cy="20" r="6" fill="#FFA500"/>
    <circle cx="60" cy="10" r="4" fill="#FF8C00"/>
  </g>
  
  <!-- Main logo (larger) -->
  <g transform="translate(250, 150)">
    <circle cx="0" cy="0" r="65" fill="url(#wideLogoGradient)" stroke="#0F1419" stroke-width="4" filter="url(#wideShadow)"/>
    <ellipse cx="0" cy="-18" rx="28" ry="22" fill="url(#wideHelmetGradient)" stroke="#0F1419" stroke-width="3"/>
    <circle cx="0" cy="-28" r="7" fill="#FFD700" stroke="#FFA500" stroke-width="1" filter="url(#wideGlow)"/>
    <circle cx="0" cy="-28" r="3.5" fill="#FFFF99"/>
    <circle cx="0" cy="12" r="22" fill="#FFD700" stroke="#FFA500" stroke-width="3"/>
    <ellipse cx="-8" cy="5" rx="3.5" ry="4.5" fill="#0F1419"/>
    <ellipse cx="8" cy="5" rx="3.5" ry="4.5" fill="#0F1419"/>
    <path d="M -12 18 Q 0 28 12 18" stroke="#0F1419" stroke-width="3.5" fill="none" stroke-linecap="round"/>
  </g>
  
  <!-- Main title -->
  <g filter="url(#wideShadow)">
    <text x="600" y="170" font-family="Arial Black, Arial, sans-serif" font-size="108" font-weight="900" text-anchor="middle" fill="url(#wideTextGradient)" filter="url(#wideGlow)">HEHE</text>
    <text x="600" y="220" font-family="Arial, sans-serif" font-size="42" font-weight="bold" text-anchor="middle" fill="#FFD700" opacity="0.9">MINER</text>
  </g>
  
  <!-- Tagline -->
  <text x="600" y="260" font-family="Arial, sans-serif" font-size="20" font-weight="normal" text-anchor="middle" fill="#FFA500" opacity="0.8">The Ultimate Crypto Mining Adventure</text>
  
  <!-- Border -->
  <rect x="8" y="8" width="1184" height="284" fill="none" stroke="url(#wideTextGradient)" stroke-width="3" rx="17" opacity="0.6"/>
</svg>

<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Background Gradients -->
    <radialGradient id="horizontalBg" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#0F0F23;stop-opacity:1" />
      <stop offset="60%" style="stop-color:#1A1A3A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
    </radialGradient>
    
    <linearGradient id="hGoldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="hHelmetGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="hAirdropGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00FF88;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#00CC66;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00AA44;stop-opacity:1" />
    </linearGradient>
    
    <!-- Effects -->
    <filter id="hGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <filter id="hStrongGlow" x="-100%" y="-100%" width="300%" height="300%">
      <feGaussianBlur stdDeviation="12" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <filter id="hShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="6" stdDeviation="8" flood-color="#000000" flood-opacity="0.6"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#horizontalBg)"/>
  
  <!-- Background pattern -->
  <g opacity="0.08">
    <pattern id="hHexPattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <polygon points="20,5 35,15 35,25 20,35 5,25 5,15" fill="none" stroke="#FFD700" stroke-width="1"/>
    </pattern>
    <rect width="1200" height="630" fill="url(#hHexPattern)"/>
  </g>
  
  <!-- Left side - Logo -->
  <g transform="translate(200, 315)">
    <circle cx="0" cy="0" r="80" fill="url(#hGoldGradient)" stroke="#000000" stroke-width="4" filter="url(#hShadow)"/>
    
    <!-- Miner helmet -->
    <ellipse cx="0" cy="-20" rx="35" ry="28" fill="url(#hHelmetGradient)" stroke="#000000" stroke-width="3"/>
    
    <!-- Helmet light -->
    <circle cx="0" cy="-35" r="10" fill="#FFD700" stroke="#FFA500" stroke-width="1" filter="url(#hStrongGlow)"/>
    <circle cx="0" cy="-35" r="5" fill="#FFFF99"/>
    
    <!-- Face -->
    <circle cx="0" cy="15" r="25" fill="#FFD700" stroke="#FFA500" stroke-width="3"/>
    
    <!-- Eyes -->
    <ellipse cx="-10" cy="8" rx="4" ry="5" fill="#000000"/>
    <ellipse cx="10" cy="8" rx="4" ry="5" fill="#000000"/>
    
    <!-- Smile -->
    <path d="M -15 22 Q 0 32 15 22" stroke="#000000" stroke-width="4" fill="none" stroke-linecap="round"/>
  </g>
  
  <!-- Right side - Text content -->
  <g transform="translate(600, 200)">
    <!-- HEHE TOKEN -->
    <text x="0" y="0" font-family="Arial Black, Arial, sans-serif" font-size="64" font-weight="900" text-anchor="middle" fill="url(#hGoldGradient)" filter="url(#hGlow)">HEHE TOKEN</text>
    
    <!-- AIRDROP banner -->
    <g transform="translate(0, 80)">
      <rect x="-180" y="-25" width="360" height="50" rx="25" fill="url(#hAirdropGradient)" filter="url(#hStrongGlow)"/>
      <rect x="-180" y="-25" width="360" height="50" rx="25" fill="none" stroke="#FFFFFF" stroke-width="2"/>
      <text x="0" y="8" font-family="Arial Black, Arial, sans-serif" font-size="28" font-weight="900" text-anchor="middle" fill="#FFFFFF">AIRDROP LIVE</text>
    </g>
    
    <!-- Features -->
    <text x="0" y="140" font-family="Arial, sans-serif" font-size="22" font-weight="bold" text-anchor="middle" fill="#FFFFFF">FREE TOKENS FOR EARLY MINERS</text>
    <text x="0" y="170" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#00FF88">Mine Every 4 Hours • Refer Friends • Upgrade Power</text>
  </g>
  
  <!-- Call to action -->
  <g transform="translate(600, 500)">
    <rect x="-200" y="-30" width="400" height="60" rx="30" fill="#FF4444" filter="url(#hStrongGlow)"/>
    <rect x="-200" y="-30" width="400" height="60" rx="30" fill="none" stroke="#FFFFFF" stroke-width="3"/>
    <text x="0" y="-5" font-family="Arial Black, Arial, sans-serif" font-size="24" font-weight="900" text-anchor="middle" fill="#FFFFFF">START MINING NOW!</text>
    <text x="0" y="20" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#FFFFFF">t.me/HeheMinerBot</text>
  </g>
  
  <!-- Decorative elements -->
  <g opacity="0.6">
    <!-- Left sparkles -->
    <polygon points="100,100 105,110 115,105 105,115 100,125 95,115 85,105 95,110" fill="#FFD700" filter="url(#hGlow)"/>
    <polygon points="120,450 123,456 129,453 123,459 120,465 117,459 111,453 117,456" fill="#FFA500" filter="url(#hGlow)"/>
    
    <!-- Right sparkles -->
    <polygon points="1000,120 1005,130 1015,125 1005,135 1000,145 995,135 985,125 995,130" fill="#FFD700" filter="url(#hGlow)"/>
    <polygon points="1050,480 1053,486 1059,483 1053,489 1050,495 1047,489 1041,483 1047,486" fill="#FFA500" filter="url(#hGlow)"/>
    
    <!-- Floating coins -->
    <circle cx="80" cy="200" r="8" fill="#FFD700"/>
    <circle cx="1100" cy="180" r="6" fill="#FFA500"/>
    <circle cx="90" cy="450" r="7" fill="#FF8C00"/>
    <circle cx="1080" cy="420" r="9" fill="#FFD700"/>
  </g>
  
  <!-- Border -->
  <rect x="8" y="8" width="1184" height="614" fill="none" stroke="url(#hGoldGradient)" stroke-width="6" rx="20" opacity="0.7"/>
</svg>

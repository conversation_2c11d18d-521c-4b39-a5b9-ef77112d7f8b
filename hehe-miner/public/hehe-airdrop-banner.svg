<svg width="1080" height="1080" viewBox="0 0 1080 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Background Gradients -->
    <radialGradient id="mainBg" cx="50%" cy="30%" r="80%">
      <stop offset="0%" style="stop-color:#F8F9FA;stop-opacity:1" />
      <stop offset="40%" style="stop-color:#E9ECEF;stop-opacity:1" />
      <stop offset="80%" style="stop-color:#DEE2E6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#CED4DA;stop-opacity:1" />
    </radialGradient>
    
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#B8860B;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#DAA520;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#CD853F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B8860B;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="helmetGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#495057;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#343A40;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="airdropGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#28A745;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#20C997;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#17A2B8;stop-opacity:1" />
    </linearGradient>
    
    <!-- Effects -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <filter id="strongGlow" x="-100%" y="-100%" width="300%" height="300%">
      <feGaussianBlur stdDeviation="15" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="12" flood-color="#000000" flood-opacity="0.6"/>
    </filter>
    
    <!-- Animated sparkles -->
    <g id="sparkle">
      <polygon points="0,-8 2,-2 8,0 2,2 0,8 -2,2 -8,0 -2,-2" fill="#FFD700"/>
    </g>
  </defs>
  
  <!-- Background -->
  <rect width="1080" height="1080" fill="url(#mainBg)"/>
  
  <!-- Background pattern -->
  <g opacity="0.15">
    <pattern id="hexPattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
      <polygon points="30,10 50,20 50,40 30,50 10,40 10,20" fill="none" stroke="#B8860B" stroke-width="1"/>
    </pattern>
    <rect width="1080" height="1080" fill="url(#hexPattern)"/>
  </g>
  
  <!-- Floating coins background -->
  <g opacity="0.4">
    <circle cx="150" cy="200" r="12" fill="#B8860B"/>
    <circle cx="900" cy="180" r="8" fill="#DAA520"/>
    <circle cx="200" cy="850" r="10" fill="#CD853F"/>
    <circle cx="850" cy="900" r="14" fill="#B8860B"/>
    <circle cx="100" cy="500" r="6" fill="#DAA520"/>
    <circle cx="950" cy="450" r="9" fill="#B8860B"/>
  </g>
  
  <!-- Main logo circle -->
  <g transform="translate(540, 300)">
    <circle cx="0" cy="0" r="120" fill="url(#goldGradient)" stroke="#343A40" stroke-width="6" filter="url(#shadow)"/>

    <!-- Miner helmet -->
    <ellipse cx="0" cy="-30" rx="50" ry="40" fill="url(#helmetGradient)" stroke="#343A40" stroke-width="4"/>

    <!-- Helmet light -->
    <circle cx="0" cy="-50" r="15" fill="#FFC107" stroke="#FF8F00" stroke-width="2" filter="url(#strongGlow)"/>
    <circle cx="0" cy="-50" r="8" fill="#FFF3C4"/>

    <!-- Face -->
    <circle cx="0" cy="20" r="35" fill="#FFC107" stroke="#FF8F00" stroke-width="4"/>

    <!-- Eyes -->
    <ellipse cx="-15" cy="10" rx="6" ry="8" fill="#212529"/>
    <ellipse cx="15" cy="10" rx="6" ry="8" fill="#212529"/>

    <!-- Smile -->
    <path d="M -20 30 Q 0 45 20 30" stroke="#212529" stroke-width="6" fill="none" stroke-linecap="round"/>
  </g>
  
  <!-- HEHE TOKEN text -->
  <g filter="url(#shadow)">
    <text x="540" y="500" font-family="Arial Black, Arial, sans-serif" font-size="84" font-weight="900" text-anchor="middle" fill="url(#goldGradient)" filter="url(#glow)">HEHE</text>
    <text x="540" y="570" font-family="Arial Black, Arial, sans-serif" font-size="54" font-weight="900" text-anchor="middle" fill="url(#goldGradient)" filter="url(#glow)">TOKEN</text>
  </g>
  
  <!-- AIRDROP banner -->
  <g transform="translate(540, 650)">
    <rect x="-200" y="-30" width="400" height="60" rx="30" fill="url(#airdropGradient)" filter="url(#strongGlow)"/>
    <rect x="-200" y="-30" width="400" height="60" rx="30" fill="none" stroke="#FFFFFF" stroke-width="3"/>
    <text x="0" y="10" font-family="Arial Black, Arial, sans-serif" font-size="36" font-weight="900" text-anchor="middle" fill="#FFFFFF">AIRDROP</text>
  </g>
  
  <!-- Key features -->
  <g transform="translate(540, 750)">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="32" font-weight="bold" text-anchor="middle" fill="#FFFFFF">FREE TOKENS FOR EARLY MINERS</text>
    <text x="0" y="45" font-family="Arial, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="#00FF88">Mine Every 4 Hours</text>
    <text x="0" y="85" font-family="Arial, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="#00FF88">Refer Friends and Earn More</text>
    <text x="0" y="125" font-family="Arial, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="#00FF88">Upgrade Mining Power</text>
  </g>
  
  <!-- Call to action -->
  <g transform="translate(540, 950)">
    <rect x="-250" y="-35" width="500" height="70" rx="35" fill="#FF4444" filter="url(#strongGlow)"/>
    <rect x="-250" y="-35" width="500" height="70" rx="35" fill="none" stroke="#FFFFFF" stroke-width="4"/>
    <text x="0" y="5" font-family="Arial Black, Arial, sans-serif" font-size="32" font-weight="900" text-anchor="middle" fill="#FFFFFF">START MINING NOW!</text>
    <text x="0" y="35" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#FFFFFF">t.me/HeheMinerBot</text>
  </g>
  
  <!-- Decorative sparkles -->
  <g opacity="0.8">
    <use href="#sparkle" transform="translate(200, 150) scale(1.5)" fill="#FFD700" filter="url(#glow)"/>
    <use href="#sparkle" transform="translate(880, 200) scale(1.2)" fill="#FFA500" filter="url(#glow)"/>
    <use href="#sparkle" transform="translate(150, 800) scale(1.3)" fill="#FF8C00" filter="url(#glow)"/>
    <use href="#sparkle" transform="translate(900, 850) scale(1.4)" fill="#FFD700" filter="url(#glow)"/>
    <use href="#sparkle" transform="translate(100, 400) scale(1.1)" fill="#FFA500" filter="url(#glow)"/>
    <use href="#sparkle" transform="translate(950, 500) scale(1.6)" fill="#FFD700" filter="url(#glow)"/>
  </g>
  
  <!-- Border -->
  <rect x="10" y="10" width="1060" height="1060" fill="none" stroke="url(#goldGradient)" stroke-width="8" rx="30" opacity="0.8"/>
</svg>

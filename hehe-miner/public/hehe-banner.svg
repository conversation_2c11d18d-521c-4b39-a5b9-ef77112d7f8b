<svg width="800" height="200" viewBox="0 0 800 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <defs>
    <linearGradient id="bannerBgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1A202C;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#2D3748;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#4A5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="helmetGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
    <!-- Glow effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <!-- Shadow effect -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="200" fill="url(#bannerBgGradient)" rx="15"/>
  
  <!-- Decorative mining elements -->
  <!-- Left side pickaxes -->
  <g transform="translate(50, 50)" opacity="0.3">
    <line x1="0" y1="0" x2="30" y2="30" stroke="#FFD700" stroke-width="3" stroke-linecap="round"/>
    <line x1="30" y1="30" x2="35" y2="25" stroke="#8B4513" stroke-width="4" stroke-linecap="round"/>
    <line x1="30" y1="30" x2="25" y2="35" stroke="#8B4513" stroke-width="4" stroke-linecap="round"/>
  </g>
  
  <!-- Right side pickaxes -->
  <g transform="translate(720, 50)" opacity="0.3">
    <line x1="0" y1="30" x2="30" y2="0" stroke="#FFD700" stroke-width="3" stroke-linecap="round"/>
    <line x1="0" y1="30" x2="5" y2="25" stroke="#8B4513" stroke-width="4" stroke-linecap="round"/>
    <line x1="0" y1="30" x2="-5" y2="35" stroke="#8B4513" stroke-width="4" stroke-linecap="round"/>
  </g>
  
  <!-- Scattered coins/gems -->
  <circle cx="100" cy="150" r="4" fill="#FFD700" opacity="0.4"/>
  <circle cx="120" cy="140" r="3" fill="#FFA500" opacity="0.4"/>
  <circle cx="680" cy="150" r="4" fill="#FFD700" opacity="0.4"/>
  <circle cx="700" cy="140" r="3" fill="#FFA500" opacity="0.4"/>
  
  <!-- Main logo circle (centered left) -->
  <g transform="translate(180, 100)">
    <!-- Main background circle -->
    <circle cx="0" cy="0" r="45" fill="url(#logoGradient)" stroke="#1A202C" stroke-width="3" filter="url(#shadow)"/>
    
    <!-- Miner helmet -->
    <ellipse cx="0" cy="-12" rx="20" ry="16" fill="url(#helmetGradient)" stroke="#1A202C" stroke-width="2"/>
    
    <!-- Helmet light -->
    <circle cx="0" cy="-20" r="5" fill="#FFD700" stroke="#FFA500" stroke-width="1" filter="url(#glow)"/>
    <circle cx="0" cy="-20" r="2.5" fill="#FFFF99"/>
    
    <!-- Face -->
    <circle cx="0" cy="8" r="16" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
    
    <!-- Eyes -->
    <ellipse cx="-6" cy="3" rx="2.5" ry="3" fill="#1A202C"/>
    <ellipse cx="6" cy="3" rx="2.5" ry="3" fill="#1A202C"/>
    
    <!-- Smile -->
    <path d="M -8 12 Q 0 20 8 12" stroke="#1A202C" stroke-width="2.5" fill="none" stroke-linecap="round"/>
  </g>
  
  <!-- Main title text -->
  <g filter="url(#shadow)">
    <!-- HEHE text -->
    <text x="400" y="120" font-family="Arial Black, Arial, sans-serif" font-size="72" font-weight="900" text-anchor="middle" fill="url(#textGradient)" filter="url(#glow)">HEHE</text>
    
    <!-- MINER text -->
    <text x="400" y="160" font-family="Arial, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="#FFD700" opacity="0.9">MINER</text>
  </g>
  
  <!-- Subtitle/tagline -->
  <text x="400" y="185" font-family="Arial, sans-serif" font-size="14" font-weight="normal" text-anchor="middle" fill="#FFA500" opacity="0.7">Dig Deep, Earn Big!</text>
  
  <!-- Decorative border -->
  <rect x="5" y="5" width="790" height="190" fill="none" stroke="url(#textGradient)" stroke-width="2" rx="12" opacity="0.5"/>
</svg>

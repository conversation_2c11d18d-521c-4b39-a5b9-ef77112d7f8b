<svg width="600" height="150" viewBox="0 0 600 150" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="compactBgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1A202C;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2D3748;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1A202C;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="compactLogoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="compactHelmetGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="compactTextGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
    <filter id="compactGlow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter id="compactShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="1" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="150" fill="url(#compactBgGradient)" rx="10"/>
  
  <!-- Logo -->
  <g transform="translate(100, 75)">
    <circle cx="0" cy="0" r="35" fill="url(#compactLogoGradient)" stroke="#1A202C" stroke-width="2" filter="url(#compactShadow)"/>
    <ellipse cx="0" cy="-10" rx="15" ry="12" fill="url(#compactHelmetGradient)" stroke="#1A202C" stroke-width="1.5"/>
    <circle cx="0" cy="-16" r="4" fill="#FFD700" stroke="#FFA500" stroke-width="0.5" filter="url(#compactGlow)"/>
    <circle cx="0" cy="-16" r="2" fill="#FFFF99"/>
    <circle cx="0" cy="6" r="12" fill="#FFD700" stroke="#FFA500" stroke-width="1.5"/>
    <ellipse cx="-5" cy="2" rx="2" ry="2.5" fill="#1A202C"/>
    <ellipse cx="5" cy="2" rx="2" ry="2.5" fill="#1A202C"/>
    <path d="M -6 10 Q 0 16 6 10" stroke="#1A202C" stroke-width="2" fill="none" stroke-linecap="round"/>
  </g>
  
  <!-- Text -->
  <g filter="url(#compactShadow)">
    <text x="300" y="85" font-family="Arial Black, Arial, sans-serif" font-size="48" font-weight="900" text-anchor="middle" fill="url(#compactTextGradient)" filter="url(#compactGlow)">HEHE</text>
    <text x="300" y="110" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#FFD700" opacity="0.9">MINER</text>
  </g>
  
  <!-- Decorative elements -->
  <circle cx="500" cy="40" r="3" fill="#FFD700" opacity="0.5"/>
  <circle cx="520" cy="50" r="2" fill="#FFA500" opacity="0.5"/>
  <circle cx="500" cy="110" r="3" fill="#FFD700" opacity="0.5"/>
  <circle cx="520" cy="100" r="2" fill="#FFA500" opacity="0.5"/>
  
  <!-- Border -->
  <rect x="3" y="3" width="594" height="144" fill="none" stroke="url(#compactTextGradient)" stroke-width="1.5" rx="8" opacity="0.4"/>
</svg>
